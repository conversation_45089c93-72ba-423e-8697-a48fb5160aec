import React, { useState } from 'react';
import { deleteDoctor, editDoctor } from '../../../api/DoctorApi';
import type { Doctor as DoctorType, Status } from '../../../api/DoctorApi';
import styles from '../doctor.module.css';

interface DoctorListProps {
    doctors: DoctorType[];
    onDoctorUpdated: () => void;
    onError: (error: string) => void;
    searchQuery: string;
    onClearSearch: () => void;
}

const DoctorList: React.FC<DoctorListProps> = ({ 
    doctors, 
    onDoctorUpdated, 
    onError, 
    searchQuery, 
    onClearSearch 
}) => {
    const [editingId, setEditingId] = useState<number | null>(null);
    const [editForm, setEditForm] = useState<Omit<DoctorType, 'id'>>({
        name: '',
        specialty: '',
        department: '',
        phoneNumber: 0,
        status: 'Online'
    });
    const [editFormErrors, setEditFormErrors] = useState<Partial<Record<keyof typeof editForm, string>>>({});

    // Validation helpers
    const validateField = (name: string, value: any) => {
        switch (name) {
            case 'name':
            case 'specialty':
            case 'department':
                if (!value || value.trim() === '') return 'This field is required';
                break;
            case 'phoneNumber':
                if (!value || isNaN(value) || value <= 0) return 'Please enter a valid phone number';
                break;
            case 'status':
                if (!['Online', 'Offline', 'Busy'].includes(value)) return 'Invalid status selected';
                break;
            default:
                break;
        }
        return '';
    };

    const getStatusBadge = (status: Status) => {
        const statusClass = status === 'Online' ? styles.statusOnline :
            status === 'Busy' ? styles.statusBusy : styles.statusOffline;
        const dotClass = status === 'Online' ? styles.statusDotOnline :
            status === 'Busy' ? styles.statusDotBusy : styles.statusDotOffline;

        return (
            <span className={`${styles.statusBadge} ${statusClass}`}>
                <div className={`${styles.statusDot} ${dotClass}`}></div>
                {status}
            </span>
        );
    };

    const handleDelete = async (id: number) => {
        if (!window.confirm('Are you sure you want to delete this doctor?')) return;

        try {
            await deleteDoctor(id);
            onDoctorUpdated();
        } catch {
            onError('Failed to delete doctor');
        }
    };

    const startEdit = (doctor: DoctorType) => {
        setEditingId(doctor.id);
        setEditForm({
            name: doctor.name,
            specialty: doctor.specialty,
            department: doctor.department,
            phoneNumber: doctor.phoneNumber,
            status: doctor.status
        });
        setEditFormErrors({});
    };

    const handleEditInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        setEditForm(prev => ({ ...prev, [name]: name === 'phoneNumber' ? Number(value) : value }));
        setEditFormErrors(prev => ({ ...prev, [name]: '' }));
    };

    const handleEditInputBlur = (e: React.FocusEvent<HTMLInputElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        const errorMsg = validateField(name, value);
        setEditFormErrors(prev => ({ ...prev, [name]: errorMsg }));
    };

    const handleEditSave = async (id: number) => {
        const errors: typeof editFormErrors = {};
        (Object.keys(editForm) as (keyof typeof editForm)[]).forEach(key => {
            const msg = validateField(key, editForm[key]);
            if (msg) errors[key] = msg;
        });
        setEditFormErrors(errors);
        if (Object.values(errors).some(Boolean)) return;

        try {
            await editDoctor(id, { id, ...editForm });
            setEditingId(null);
            onDoctorUpdated();
        } catch {
            onError('Failed to update doctor');
        }
    };

    const handleEditCancel = () => {
        setEditingId(null);
        setEditFormErrors({});
    };

    return (
        <div className={styles.card}>
            <div className={styles.cardHeader}>
                <h2 className={styles.cardTitle}>
                    📋 {searchQuery
                        ? `Search Results (${doctors.length} found)` 
                        : `All Doctors (${doctors.length})`}
                </h2>
                {searchQuery && (
                    <button
                        type="button"
                        onClick={onClearSearch}
                        className={`${styles.button} ${styles.buttonSecondary}`}
                    >
                        🗑️ Clear Search
                    </button>
                )}
            </div>

            {doctors.length === 0 ? (
                <div className={styles.emptyState}>
                    <div className={styles.emptyStateIcon}>
                        {searchQuery ? '🔍' : '👥'}
                    </div>
                    <h3 className={styles.emptyStateTitle}>
                        {searchQuery 
                            ? 'No doctors match your search' 
                            : 'No doctors found'}
                    </h3>
                    <p className={styles.emptyStateText}>
                        {searchQuery 
                            ? 'Try adjusting your search criteria or clear the search to see all doctors.' 
                            : 'Get started by adding your first doctor.'}
                    </p>
                </div>
            ) : (
                <div className={styles.tableContainer}>
                    <table className={styles.table}>
                        <thead className={styles.tableHeader}>
                            <tr>
                                <th className={styles.tableHeaderCell}>ID</th>
                                <th className={styles.tableHeaderCell}>Name</th>
                                <th className={styles.tableHeaderCell}>Specialty</th>
                                <th className={styles.tableHeaderCell}>Department</th>
                                <th className={styles.tableHeaderCell}>Phone</th>
                                <th className={styles.tableHeaderCell}>Status</th>
                                <th className={styles.tableHeaderCell}>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {doctors.map((doctor) => (
                                <tr key={doctor.id} className={styles.tableRow}>
                                    <td className={styles.tableCell}>
                                        {doctor.id}
                                    </td>
                                    {editingId === doctor.id ? (
                                        <>
                                            <td className={styles.tableCell}>
                                                <input
                                                    name="name"
                                                    value={editForm.name}
                                                    onChange={handleEditInputChange}
                                                    onBlur={handleEditInputBlur}
                                                    className={`${styles.editInput} ${editFormErrors.name ? styles.editError : ''}`}
                                                    placeholder="Enter doctor's name"
                                                />
                                                {editFormErrors.name && (
                                                    <div className={styles.editErrorText}>{editFormErrors.name}</div>
                                                )}
                                            </td>
                                            <td className={styles.tableCell}>
                                                <input
                                                    name="specialty"
                                                    value={editForm.specialty}
                                                    onChange={handleEditInputChange}
                                                    onBlur={handleEditInputBlur}
                                                    className={`${styles.editInput} ${editFormErrors.specialty ? styles.editError : ''}`}
                                                    placeholder='Specialty'
                                                />
                                                {editFormErrors.specialty && (
                                                    <div className={styles.editErrorText}>{editFormErrors.specialty}</div>
                                                )}
                                            </td>
                                            <td className={styles.tableCell}>
                                                <input
                                                    name="department"
                                                    value={editForm.department}
                                                    onChange={handleEditInputChange}
                                                    onBlur={handleEditInputBlur}
                                                    className={`${styles.editInput} ${editFormErrors.department ? styles.editError : ''}`}
                                                    placeholder='Department'
                                                />
                                                {editFormErrors.department && (
                                                    <div className={styles.editErrorText}>{editFormErrors.department}</div>
                                                )}
                                            </td>
                                            <td className={styles.tableCell}>
                                                <input
                                                    name="phoneNumber"
                                                    type="number"
                                                    value={editForm.phoneNumber}
                                                    onChange={handleEditInputChange}
                                                    onBlur={handleEditInputBlur}
                                                    className={`${styles.editInput} ${editFormErrors.phoneNumber ? styles.editError : ''}`}
                                                    placeholder='Phone Number'
                                                />
                                                {editFormErrors.phoneNumber && (
                                                    <div className={styles.editErrorText}>{editFormErrors.phoneNumber}</div>
                                                )}
                                            </td>
                                            <td className={styles.tableCell}>
                                                <select
                                                    name="status"
                                                    value={editForm.status}
                                                    onChange={handleEditInputChange}
                                                    onBlur={handleEditInputBlur}
                                                    className={`${styles.editSelect} ${editFormErrors.status ? styles.editError : ''}`}
                                                    id={`edit-status-${doctor.id}`}
                                                    aria-label="Status"
                                                >
                                                    <option value="Online">Online</option>
                                                    <option value="Offline">Offline</option>
                                                    <option value="Busy">Busy</option>
                                                </select>
                                                {editFormErrors.status && (
                                                    <div className={styles.editErrorText}>{editFormErrors.status}</div>
                                                )}
                                            </td>
                                            <td className={styles.tableCell}>
                                                <div className={styles.actionButtons}>
                                                    <button
                                                        type="button"
                                                        onClick={() => handleEditSave(doctor.id)}
                                                        className={`${styles.button} ${styles.buttonSuccess} ${styles.iconButton}`}
                                                        title="Save changes"
                                                    >
                                                        💾 Save
                                                    </button>
                                                    <button
                                                        type="button"
                                                        onClick={handleEditCancel}
                                                        className={`${styles.button} ${styles.buttonSecondary} ${styles.iconButton}`}
                                                        title="Cancel editing"
                                                    >
                                                        ❌ Cancel
                                                    </button>
                                                </div>
                                            </td>
                                        </>
                                    ) : (
                                        <>
                                            <td className={`${styles.tableCell} ${styles.tableCellName}`}>
                                                {doctor.name}
                                            </td>
                                            <td className={styles.tableCell}>
                                                {doctor.specialty}
                                            </td>
                                            <td className={styles.tableCell}>
                                                {doctor.department}
                                            </td>
                                            <td className={`${styles.tableCell} ${styles.tableCellPhone}`}>
                                                {doctor.phoneNumber.toLocaleString()}
                                            </td>
                                            <td className={styles.tableCell}>
                                                {getStatusBadge(doctor.status)}
                                            </td>
                                            <td className={styles.tableCell}>
                                                <div className={styles.actionButtons}>
                                                    <button
                                                        type="button"
                                                        onClick={() => startEdit(doctor)}
                                                        className={`${styles.button} ${styles.buttonSecondary} ${styles.iconButton}`}
                                                        title="Edit doctor"
                                                    >
                                                        ✏️ Edit
                                                    </button>
                                                    <button
                                                        type="button"
                                                        onClick={() => handleDelete(doctor.id)}
                                                        className={`${styles.button} ${styles.buttonDanger} ${styles.iconButton}`}
                                                        title="Delete doctor"
                                                    >
                                                        🗑️ Delete
                                                    </button>
                                                </div>
                                            </td>
                                        </>
                                    )}
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            )}
        </div>
    );
};

export default DoctorList;
