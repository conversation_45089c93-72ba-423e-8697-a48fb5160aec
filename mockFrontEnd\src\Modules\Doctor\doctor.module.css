/* Doctor Management Page Styles */

.container {
    min-height: 100vh;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding: 2rem 1rem;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.wrapper {
    max-width: 1200px;
    margin: 0 auto;
}

/* Header Styles */
.header {
    margin-bottom: 2rem;
    text-align: center;
}

.headerTitle {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    margin-bottom: 0.5rem;
    font-size: 2.5rem;
    font-weight: 700;
    color: #1a202c;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.headerSubtitle {
    color: #4a5568;
    font-size: 1.1rem;
    font-weight: 400;
}

/* Card Styles */
.card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    margin-bottom: 2rem;
    overflow: hidden;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.cardHeader {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e2e8f0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.cardTitle {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
}

/* Form Styles */
.form {
    padding: 2rem;
}

.formGrid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.formGroup {
    display: flex;
    flex-direction: column;
}

.label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
}

.input,
.select {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    background: white;
}

.input:focus,
.select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.input:hover,
.select:hover {
    border-color: #cbd5e0;
}

.input.error,
.select.error {
    border-color: #ef4444;
    background-color: #fef2f2;
}

.errorMessage {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    color: #ef4444;
    font-size: 0.75rem;
    margin-top: 0.25rem;
    font-weight: 500;
}

.formActions {
    display: flex;
    justify-content: flex-end;
    margin-top: 1.5rem;
}

/* Button Styles */
.button {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
}

.buttonPrimary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.buttonPrimary:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
}

.buttonPrimary:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.buttonSecondary {
    background: #f8fafc;
    color: #475569;
    border: 1px solid #e2e8f0;
}

.buttonSecondary:hover {
    background: #f1f5f9;
    border-color: #cbd5e0;
}

.buttonSuccess {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    padding: 0.5rem;
}

.buttonSuccess:hover {
    transform: scale(1.05);
}

.buttonDanger {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    padding: 0.5rem;
}

.buttonDanger:hover {
    transform: scale(1.05);
}

.iconButton {
    padding: 0.5rem;
    border-radius: 6px;
    transition: all 0.2s ease;
}

/* Loading and Error States */
.loadingContainer,
.errorContainer {
    min-height: 50vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.loadingContent,
.errorContent {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    text-align: center;
    max-width: 400px;
    width: 100%;
}

.loadingText {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #4b5563;
    font-size: 1rem;
}

.errorTitle {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    color: #ef4444;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.errorMessage {
    color: #6b7280;
    margin-bottom: 1.5rem;
}

/* Table Styles */
.tableContainer {
    overflow-x: auto;
    border-radius: 0 0 16px 16px;
}

.table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
}

.tableHeader {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.tableHeaderCell {
    padding: 1rem 1.5rem;
    text-align: left;
    font-size: 0.75rem;
    font-weight: 700;
    color: #374151;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border-bottom: 2px solid #e2e8f0;
}

.tableRow {
    transition: background-color 0.2s ease;
    border-bottom: 1px solid #f1f5f9;
}

.tableRow:hover {
    background-color: #f8fafc;
}

.tableCell {
    padding: 1rem 1.5rem;
    vertical-align: middle;
}

.tableCellName {
    font-weight: 600;
    color: #1a202c;
}

.tableCellPhone {
    font-family: 'Monaco', 'Menlo', monospace;
    color: #4a5568;
}

/* Status Badge */
.statusBadge {
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
    padding: 0.375rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    border: 1px solid;
}

.statusOnline {
    background-color: #dcfce7;
    color: #166534;
    border-color: #bbf7d0;
}

.statusOffline {
    background-color: #f3f4f6;
    color: #374151;
    border-color: #d1d5db;
}

.statusBusy {
    background-color: #fef3c7;
    color: #92400e;
    border-color: #fde68a;
}

.statusDot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
}

.statusDotOnline {
    background-color: #22c55e;
}

.statusDotOffline {
    background-color: #6b7280;
}

.statusDotBusy {
    background-color: #f59e0b;
}

/* Edit Form in Table */
.editInput,
.editSelect {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 0.875rem;
}

.editInput:focus,
.editSelect:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.editError {
    border-color: #ef4444;
    background-color: #fef2f2;
}

.editErrorText {
    color: #ef4444;
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

.actionButtons {
    display: flex;
    gap: 0.5rem;
}

/* Empty State */
.emptyState {
    padding: 3rem;
    text-align: center;
    color: #6b7280;
}

.emptyStateIcon {
    margin: 0 auto 1rem;
    color: #d1d5db;
}

.emptyStateTitle {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1a202c;
    margin-bottom: 0.5rem;
}

.emptyStateText {
    color: #6b7280;
}

/* Animations */
@keyframes spin {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

.spinning {
    animation: spin 1s linear infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 1rem 0.5rem;
    }

    .headerTitle {
        font-size: 2rem;
    }

    .formGrid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .card {
        margin-bottom: 1rem;
    }

    .cardHeader,
    .form {
        padding: 1rem;
    }

    .tableHeaderCell,
    .tableCell {
        padding: 0.75rem 0.5rem;
    }

    .actionButtons {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .headerTitle {
        font-size: 1.5rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .tableContainer {
        font-size: 0.75rem;
    }

    .statusBadge {
        font-size: 0.625rem;
        padding: 0.25rem 0.5rem;
    }
}