import React, { useState } from 'react';
import { addDoctor } from '../../../api/DoctorApi';
import type { Doctor as DoctorType, Status } from '../../../api/DoctorApi';
import styles from '../doctor.module.css';

interface AddNewDoctorProps {
    onDoctorAdded: () => void;
    onCancel: () => void;
    onError: (error: string) => void;
}

const AddNewDoctor: React.FC<AddNewDoctorProps> = ({ onDoctorAdded, onCancel, onError }) => {
    const [form, setForm] = useState<Omit<DoctorType, 'id'>>({
        name: '',
        specialty: '',
        department: '',
        phoneNumber: 0,
        status: 'Online'
    });
    const [formErrors, setFormErrors] = useState<Partial<Record<keyof typeof form, string>>>({});
    const [creating, setCreating] = useState(false);

    // Validation helpers
    const validateField = (name: string, value: any) => {
        switch (name) {
            case 'name':
            case 'specialty':
            case 'department':
                if (!value || value.trim() === '') return 'This field is required';
                break;
            case 'phoneNumber':
                if (!value || isNaN(value) || value <= 0) return 'Please enter a valid phone number';
                break;
            case 'status':
                if (!['Online', 'Offline', 'Busy'].includes(value)) return 'Invalid status selected';
                break;
            default:
                break;
        }
        return '';
    };

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        setForm(prev => ({ ...prev, [name]: name === 'phoneNumber' ? Number(value) : value }));
        setFormErrors(prev => ({ ...prev, [name]: '' }));
    };

    const handleInputBlur = (e: React.FocusEvent<HTMLInputElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        const errorMsg = validateField(name, value);
        setFormErrors(prev => ({ ...prev, [name]: errorMsg }));
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        const errors: typeof formErrors = {};
        (Object.keys(form) as (keyof typeof form)[]).forEach(key => {
            const msg = validateField(key, form[key]);
            if (msg) errors[key] = msg;
        });
        setFormErrors(errors);
        if (Object.values(errors).some(Boolean)) return;

        setCreating(true);
        try {
            await addDoctor(form as DoctorType);
            setForm({ name: '', specialty: '', department: '', phoneNumber: 0, status: 'Online' });
            setFormErrors({});
            onDoctorAdded();
        } catch {
            onError('Failed to create doctor');
        } finally {
            setCreating(false);
        }
    };

    return (
        <div className={styles.card}>
            <div className={styles.cardHeader}>
                <h2 className={styles.cardTitle}>
                    ➕ Add New Doctor
                </h2>
                <button
                    type="button"
                    onClick={onCancel}
                    className={`${styles.button} ${styles.buttonSecondary}`}
                >
                    ✕ Cancel
                </button>
            </div>

            <form onSubmit={handleSubmit} className={styles.form}>
                <div className={styles.formGrid}>
                    {/* Name Field */}
                    <div className={styles.formGroup}>
                        <label className={styles.label}>
                            👤 Name
                        </label>
                        <input
                            name="name"
                            value={form.name}
                            onChange={handleInputChange}
                            onBlur={handleInputBlur}
                            placeholder="Enter doctor's name"
                            className={`${styles.input} ${formErrors.name ? styles.error : ''}`}
                            required
                        />
                        {formErrors.name && (
                            <div className={styles.errorMessage}>
                                ⚠️ {formErrors.name}
                            </div>
                        )}
                    </div>

                    {/* Specialty Field */}
                    <div className={styles.formGroup}>
                        <label className={styles.label}>
                            🩺 Specialty
                        </label>
                        <input
                            name="specialty"
                            value={form.specialty}
                            onChange={handleInputChange}
                            onBlur={handleInputBlur}
                            placeholder="Enter specialty"
                            className={`${styles.input} ${formErrors.specialty ? styles.error : ''}`}
                            required
                        />
                        {formErrors.specialty && (
                            <div className={styles.errorMessage}>
                                ⚠️ {formErrors.specialty}
                            </div>
                        )}
                    </div>

                    {/* Department Field */}
                    <div className={styles.formGroup}>
                        <label className={styles.label}>
                            🏢 Department
                        </label>
                        <input
                            name="department"
                            value={form.department}
                            onChange={handleInputChange}
                            onBlur={handleInputBlur}
                            placeholder="Enter department"
                            className={`${styles.input} ${formErrors.department ? styles.error : ''}`}
                            required
                        />
                        {formErrors.department && (
                            <div className={styles.errorMessage}>
                                ⚠️ {formErrors.department}
                            </div>
                        )}
                    </div>

                    {/* Phone Number Field */}
                    <div className={styles.formGroup}>
                        <label className={styles.label}>
                            📞 Phone Number
                        </label>
                        <input
                            name="phoneNumber"
                            type="number"
                            value={form.phoneNumber || ''}
                            onChange={handleInputChange}
                            onBlur={handleInputBlur}
                            placeholder="Enter phone number"
                            className={`${styles.input} ${formErrors.phoneNumber ? styles.error : ''}`}
                            required
                        />
                        {formErrors.phoneNumber && (
                            <div className={styles.errorMessage}>
                                ⚠️ {formErrors.phoneNumber}
                            </div>
                        )}
                    </div>

                    {/* Status Field */}
                    <div className={styles.formGroup}>
                        <label className={styles.label}>
                            📊 Status
                        </label>
                        <select
                            name="status"
                            value={form.status}
                            onChange={handleInputChange}
                            onBlur={handleInputBlur}
                            className={`${styles.select} ${formErrors.status ? styles.error : ''}`}
                            aria-label="Status"
                            required
                        >
                            <option value="Online">Online</option>
                            <option value="Offline">Offline</option>
                            <option value="Busy">Busy</option>
                        </select>
                        {formErrors.status && (
                            <div className={styles.errorMessage}>
                                ⚠️ {formErrors.status}
                            </div>
                        )}
                    </div>
                </div>

                <div className={styles.formActions}>
                    <button
                        type="submit"
                        disabled={creating}
                        className={`${styles.button} ${styles.buttonPrimary}`}
                    >
                        {creating ? (
                            <>
                                ⏳ Adding...
                            </>
                        ) : (
                            <>
                                ➕ Add Doctor
                            </>
                        )}
                    </button>
                </div>
            </form>
        </div>
    );
};

export default AddNewDoctor;
