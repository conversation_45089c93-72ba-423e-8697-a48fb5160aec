import React, { useEffect, useState } from 'react';
import { getAllDoc<PERSON>, addDoctor, deleteDoctor, editDoctor } from '../../api/DoctorApi';
import type { Doctor as DoctorType, Status } from '../../api/DoctorApi';
import {
    UserPlus,
    Edit3,
    Trash2,
    Save,
    X,
    Users,
    Phone,
    Building,
    Stethoscope,
    AlertCircle,
    Loader2
} from 'lucide-react';
import styles from './doctor.module.css';

function Doctor() {
    const [doctors, setDoctors] = useState<DoctorType[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [form, setForm] = useState<Omit<DoctorType, 'id'>>(
        { name: '', specialty: '', department: '', phoneNumber: 0, status: 'Online' }
    );
    const [formErrors, setFormErrors] = useState<Partial<Record<keyof typeof form, string>>>({});
    const [creating, setCreating] = useState(false);
    const [editingId, setEditingId] = useState<number | null>(null);
    const [editForm, setEditForm] = useState<Omit<DoctorType, 'id'>>({ name: '', specialty: '', department: '', phoneNumber: 0, status: 'Online' });
    const [editFormErrors, setEditFormErrors] = useState<Partial<Record<keyof typeof editForm, string>>>({});

    const fetchDoctors = () => {
        setLoading(true);
        setError(null);
        getAllDoctors()
            .then(res => {
                setDoctors(res.data);
                setLoading(false);
            })
            .catch(() => {
                setError('Failed to fetch doctors');
                setLoading(false);
            });
    };

    useEffect(() => {
        fetchDoctors();
    }, []);

    // Validation helpers
    const validateField = (name: string, value: any) => {
        switch (name) {
            case 'name':
            case 'specialty':
            case 'department':
                if (!value || value.trim() === '') return 'This field is required';
                break;
            case 'phoneNumber':
                if (!value || isNaN(value) || value <= 0) return 'Please enter a valid phone number';
                break;
            case 'status':
                if (!['Online', 'Offline', 'Busy'].includes(value)) return 'Invalid status selected';
                break;
            default:
                break;
        }
        return '';
    };

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        setForm(prev => ({ ...prev, [name]: name === 'phoneNumber' ? Number(value) : value }));
        setFormErrors(prev => ({ ...prev, [name]: '' }));
    };

    const handleInputBlur = (e: React.FocusEvent<HTMLInputElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        const errorMsg = validateField(name, value);
        setFormErrors(prev => ({ ...prev, [name]: errorMsg }));
    };

    const handleCreate = async (e: React.FormEvent) => {
        e.preventDefault();
        const errors: typeof formErrors = {};
        (Object.keys(form) as (keyof typeof form)[]).forEach(key => {
            const msg = validateField(key, form[key]);
            if (msg) errors[key] = msg;
        });
        setFormErrors(errors);
        if (Object.values(errors).some(Boolean)) return;

        setCreating(true);
        try {
            await addDoctor(form as DoctorType);
            setForm({ name: '', specialty: '', department: '', phoneNumber: 0, status: 'Online' });
            setFormErrors({});
            fetchDoctors();
        } catch {
            setError('Failed to create doctor');
        } finally {
            setCreating(false);
        }
    };

    const handleDelete = async (id: number) => {
        if (!window.confirm('Are you sure you want to delete this doctor?')) return;

        try {
            await deleteDoctor(id);
            fetchDoctors();
        } catch {
            setError('Failed to delete doctor');
        }
    };

    const startEdit = (doctor: DoctorType) => {
        setEditingId(doctor.id);
        setEditForm({
            name: doctor.name,
            specialty: doctor.specialty,
            department: doctor.department,
            phoneNumber: doctor.phoneNumber,
            status: doctor.status
        });
        setEditFormErrors({});
    };

    const handleEditInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        setEditForm(prev => ({ ...prev, [name]: name === 'phoneNumber' ? Number(value) : value }));
        setEditFormErrors(prev => ({ ...prev, [name]: '' }));
    };

    const handleEditInputBlur = (e: React.FocusEvent<HTMLInputElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        const errorMsg = validateField(name, value);
        setEditFormErrors(prev => ({ ...prev, [name]: errorMsg }));
    };

    const handleEditSave = async (id: number) => {
        const errors: typeof editFormErrors = {};
        (Object.keys(editForm) as (keyof typeof editForm)[]).forEach(key => {
            const msg = validateField(key, editForm[key]);
            if (msg) errors[key] = msg;
        });
        setEditFormErrors(errors);
        if (Object.values(errors).some(Boolean)) return;

        try {
            await editDoctor(id, { id, ...editForm });
            setEditingId(null);
            fetchDoctors();
        } catch {
            setError('Failed to update doctor');
        }
    };

    const handleEditCancel = () => {
        setEditingId(null);
        setEditFormErrors({});
    };

    const getStatusBadge = (status: Status) => {
        const statusClass = status === 'Online' ? styles.statusOnline :
            status === 'Busy' ? styles.statusBusy : styles.statusOffline;
        const dotClass = status === 'Online' ? styles.statusDotOnline :
            status === 'Busy' ? styles.statusDotBusy : styles.statusDotOffline;

        return (
            <span className={`${styles.statusBadge} ${statusClass}`}>
                <div className={`${styles.statusDot} ${dotClass}`}></div>
                {status}
            </span>
        );
    };

    if (loading) {
        return (
            <div className={styles.loadingContainer}>
                <div className={styles.loadingContent}>
                    <div className={styles.loadingText}>
                        <Loader2 className={styles.spinning} size={24} />
                        Loading doctors...
                    </div>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className={styles.errorContainer}>
                <div className={styles.errorContent}>
                    <div className={styles.errorTitle}>
                        <AlertCircle size={24} />
                        Error
                    </div>
                    <p className={styles.errorMessage}>{error}</p>
                    <button
                        onClick={fetchDoctors}
                        className={`${styles.button} ${styles.buttonPrimary}`}
                    >
                        Retry
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div className={styles.container}>
            <div className={styles.wrapper}>
                {/* Header */}
                <div className={styles.header}>
                    <h1 className={styles.headerTitle}>
                        <Users size={40} />
                        Doctor Management
                    </h1>
                    <p className={styles.headerSubtitle}>Manage hospital doctors and their information</p>
                </div>

                {/* Add Doctor Form */}
                <div className={styles.card}>
                    <div className={styles.cardHeader}>
                        <h2 className={styles.cardTitle}>
                            <UserPlus size={20} />
                            Add New Doctor
                        </h2>
                    </div>

                    <form onSubmit={handleCreate} className={styles.form}>
                        <div className={styles.formGrid}>
                            {/* Name Field */}
                            <div className={styles.formGroup}>
                                <label className={styles.label}>
                                    <Users size={16} />
                                    Name
                                </label>
                                <input
                                    name="name"
                                    value={form.name}
                                    onChange={handleInputChange}
                                    onBlur={handleInputBlur}
                                    placeholder="Enter doctor's name"
                                    className={`${styles.input} ${formErrors.name ? styles.error : ''}`}
                                    required
                                />
                                {formErrors.name && (
                                    <div className={styles.errorMessage}>
                                        <AlertCircle size={12} />
                                        {formErrors.name}
                                    </div>
                                )}
                            </div>

                            {/* Specialty Field */}
                            <div className={styles.formGroup}>
                                <label className={styles.label}>
                                    <Stethoscope size={16} />
                                    Specialty
                                </label>
                                <input
                                    name="specialty"
                                    value={form.specialty}
                                    onChange={handleInputChange}
                                    onBlur={handleInputBlur}
                                    placeholder="Enter specialty"
                                    className={`${styles.input} ${formErrors.specialty ? styles.error : ''}`}
                                    required
                                />
                                {formErrors.specialty && (
                                    <div className={styles.errorMessage}>
                                        <AlertCircle size={12} />
                                        {formErrors.specialty}
                                    </div>
                                )}
                            </div>

                            {/* Department Field */}
                            <div className={styles.formGroup}>
                                <label className={styles.label}>
                                    <Building size={16} />
                                    Department
                                </label>
                                <input
                                    name="department"
                                    value={form.department}
                                    onChange={handleInputChange}
                                    onBlur={handleInputBlur}
                                    placeholder="Enter department"
                                    className={`${styles.input} ${formErrors.department ? styles.error : ''}`}
                                    required
                                />
                                {formErrors.department && (
                                    <div className={styles.errorMessage}>
                                        <AlertCircle size={12} />
                                        {formErrors.department}
                                    </div>
                                )}
                            </div>

                            {/* Phone Number Field */}
                            <div className={styles.formGroup}>
                                <label className={styles.label}>
                                    <Phone size={16} />
                                    Phone Number
                                </label>
                                <input
                                    name="phoneNumber"
                                    type="number"
                                    value={form.phoneNumber || ''}
                                    onChange={handleInputChange}
                                    onBlur={handleInputBlur}
                                    placeholder="Enter phone number"
                                    className={`${styles.input} ${formErrors.phoneNumber ? styles.error : ''}`}
                                    required
                                />
                                {formErrors.phoneNumber && (
                                    <div className={styles.errorMessage}>
                                        <AlertCircle size={12} />
                                        {formErrors.phoneNumber}
                                    </div>
                                )}
                            </div>

                            {/* Status Field */}
                            <div className={styles.formGroup}>
                                <label className={styles.label}>
                                    Status
                                </label>
                                <select
                                    name="status"
                                    value={form.status}
                                    onChange={handleInputChange}
                                    onBlur={handleInputBlur}
                                    className={`${styles.select} ${formErrors.status ? styles.error : ''}`}
                                    required
                                >
                                    <option value="Online">Online</option>
                                    <option value="Offline">Offline</option>
                                    <option value="Busy">Busy</option>
                                </select>
                                {formErrors.status && (
                                    <div className={styles.errorMessage}>
                                        <AlertCircle size={12} />
                                        {formErrors.status}
                                    </div>
                                )}
                            </div>
                        </div>

                        <div className={styles.formActions}>
                            <button
                                type="submit"
                                disabled={creating}
                                className={`${styles.button} ${styles.buttonPrimary}`}
                            >
                                {creating ? (
                                    <>
                                        <Loader2 className={styles.spinning} size={16} />
                                        Adding...
                                    </>
                                ) : (
                                    <>
                                        <UserPlus size={16} />
                                        Add Doctor
                                    </>
                                )}
                            </button>
                        </div>
                    </form>
                </div>

                {/* Doctors Table */}
                <div className={styles.card}>
                    <div className={styles.cardHeader}>
                        <h2 className={styles.cardTitle}>
                            All Doctors ({doctors.length})
                        </h2>
                    </div>

                    {doctors.length === 0 ? (
                        <div className={styles.emptyState}>
                            <Users className={styles.emptyStateIcon} size={48} />
                            <h3 className={styles.emptyStateTitle}>No doctors found</h3>
                            <p className={styles.emptyStateText}>Get started by adding your first doctor.</p>
                        </div>
                    ) : (
                        <div className={styles.tableContainer}>
                            <table className={styles.table}>
                                <thead className={styles.tableHeader}>
                                    <tr>
                                        <th className={styles.tableHeaderCell}>ID</th>
                                        <th className={styles.tableHeaderCell}>Name</th>
                                        <th className={styles.tableHeaderCell}>Specialty</th>
                                        <th className={styles.tableHeaderCell}>Department</th>
                                        <th className={styles.tableHeaderCell}>Phone</th>
                                        <th className={styles.tableHeaderCell}>Status</th>
                                        <th className={styles.tableHeaderCell}>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {doctors.map((doctor) => (
                                        <tr key={doctor.id} className={styles.tableRow}>
                                            <td className={styles.tableCell}>
                                                {doctor.id}
                                            </td>
                                            {editingId === doctor.id ? (
                                                <>
                                                    <td className={styles.tableCell}>
                                                        <input
                                                            name="name"
                                                            value={editForm.name}
                                                            onChange={handleEditInputChange}
                                                            onBlur={handleEditInputBlur}
                                                            className={`${styles.editInput} ${editFormErrors.name ? styles.editError : ''}`}
                                                        />
                                                        {editFormErrors.name && (
                                                            <div className={styles.editErrorText}>{editFormErrors.name}</div>
                                                        )}
                                                    </td>
                                                    <td className={styles.tableCell}>
                                                        <input
                                                            name="specialty"
                                                            value={editForm.specialty}
                                                            onChange={handleEditInputChange}
                                                            onBlur={handleEditInputBlur}
                                                            className={`${styles.editInput} ${editFormErrors.specialty ? styles.editError : ''}`}
                                                        />
                                                        {editFormErrors.specialty && (
                                                            <div className={styles.editErrorText}>{editFormErrors.specialty}</div>
                                                        )}
                                                    </td>
                                                    <td className={styles.tableCell}>
                                                        <input
                                                            name="department"
                                                            value={editForm.department}
                                                            onChange={handleEditInputChange}
                                                            onBlur={handleEditInputBlur}
                                                            className={`${styles.editInput} ${editFormErrors.department ? styles.editError : ''}`}
                                                        />
                                                        {editFormErrors.department && (
                                                            <div className={styles.editErrorText}>{editFormErrors.department}</div>
                                                        )}
                                                    </td>
                                                    <td className={styles.tableCell}>
                                                        <input
                                                            name="phoneNumber"
                                                            type="number"
                                                            value={editForm.phoneNumber}
                                                            onChange={handleEditInputChange}
                                                            onBlur={handleEditInputBlur}
                                                            className={`${styles.editInput} ${editFormErrors.phoneNumber ? styles.editError : ''}`}
                                                        />
                                                        {editFormErrors.phoneNumber && (
                                                            <div className={styles.editErrorText}>{editFormErrors.phoneNumber}</div>
                                                        )}
                                                    </td>
                                                    <td className={styles.tableCell}>
                                                        <select
                                                            name="status"
                                                            value={editForm.status}
                                                            onChange={handleEditInputChange}
                                                            onBlur={handleEditInputBlur}
                                                            className={`${styles.editSelect} ${editFormErrors.status ? styles.editError : ''}`}
                                                        >
                                                            <option value="Online">Online</option>
                                                            <option value="Offline">Offline</option>
                                                            <option value="Busy">Busy</option>
                                                        </select>
                                                        {editFormErrors.status && (
                                                            <div className={styles.editErrorText}>{editFormErrors.status}</div>
                                                        )}
                                                    </td>
                                                    <td className={styles.tableCell}>
                                                        <div className={styles.actionButtons}>
                                                            <button
                                                                onClick={() => handleEditSave(doctor.id)}
                                                                className={`${styles.button} ${styles.buttonSuccess} ${styles.iconButton}`}
                                                                title="Save changes"
                                                            >
                                                                <Save size={16} />
                                                            </button>
                                                            <button
                                                                onClick={handleEditCancel}
                                                                className={`${styles.button} ${styles.buttonSecondary} ${styles.iconButton}`}
                                                                title="Cancel editing"
                                                            >
                                                                <X size={16} />
                                                            </button>
                                                        </div>
                                                    </td>
                                                </>
                                            ) : (
                                                <>
                                                    <td className={`${styles.tableCell} ${styles.tableCellName}`}>
                                                        {doctor.name}
                                                    </td>
                                                    <td className={styles.tableCell}>
                                                        {doctor.specialty}
                                                    </td>
                                                    <td className={styles.tableCell}>
                                                        {doctor.department}
                                                    </td>
                                                    <td className={`${styles.tableCell} ${styles.tableCellPhone}`}>
                                                        {doctor.phoneNumber.toLocaleString()}
                                                    </td>
                                                    <td className={styles.tableCell}>
                                                        {getStatusBadge(doctor.status)}
                                                    </td>
                                                    <td className={styles.tableCell}>
                                                        <div className={styles.actionButtons}>
                                                            <button
                                                                onClick={() => startEdit(doctor)}
                                                                className={`${styles.button} ${styles.buttonSecondary} ${styles.iconButton}`}
                                                                title="Edit doctor"
                                                            >
                                                                <Edit3 size={16} />
                                                            </button>
                                                            <button
                                                                onClick={() => handleDelete(doctor.id)}
                                                                className={`${styles.button} ${styles.buttonDanger} ${styles.iconButton}`}
                                                                title="Delete doctor"
                                                            >
                                                                <Trash2 size={16} />
                                                            </button>
                                                        </div>
                                                    </td>
                                                </>
                                            )}
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
}

export default Doctor;